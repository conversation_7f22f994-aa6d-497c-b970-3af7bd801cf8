import uuid
import time
import logging
import re
import json
from datetime import timedelta
from typing import Dict, Any, Optional, List
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache, CustomerPolicyWorkflowAuditLog
from .policy_tpa_service import TPAApiService
from .workflow_registry import get_workflow_registry
from .workflow_config_validator import ValidationResult
from customer.exceptions import PolicyWorkflowError, CustomerDataError

logger = logging.getLogger('django.customer_policy_crm')



# Context keys
CONTEXT_STEP_DATA = 'step_data'

# Legacy workflow type constants for backward compatibility
WORKFLOW_TYPE_POLICY_LIST = 'POLICY_LIST'
WORKFLOW_TYPE_POLICY_DETAILS = 'POLICY_DETAILS'



# Template pattern for variable resolution
TEMPLATE_VARIABLE_PATTERN = r'\{\{([^}]+)\}\}'

# Default retry settings
DEFAULT_RETRY_DELAY_SECONDS = 3
DEFAULT_MAX_RETRIES = 1

# Cache settings
DEFAULT_CACHE_DURATION_MINUTES = 1 # TODO: Change to 60-240 (1-4 hours) in production





# Response field names
FIELD_LIST_OF_SEARCH_CITIZEN_ID = 'ListOfSearchCitizenID'
FIELD_LIST_OF_CHECK_REGISTER = 'ListOfCheckRegister'
FIELD_LIST_OF_POLICY_LIST_SOCIAL = 'ListOfPolicyListSocial'
FIELD_LIST_OF_POL_DET = 'ListOfPolDet'
FIELD_LIST_OF_POL_CLAIM = 'ListOfPolClaim'
FIELD_STATUS = 'Status'
FIELD_MEMBER_CODE = 'MemberCode'

# Context keys (CONTEXT_STEP_DATA already defined above)
CONTEXT_STEP_RESULTS = 'step_results'
CONTEXT_TPA_CALLS = 'tpa_calls'
CONTEXT_TPA_TIME = 'tpa_time'
CONTEXT_CUSTOMER = 'customer'
CONTEXT_MEMBER_CODE = 'member_code'
CONTEXT_PROCESSED_DATA = 'processed_data'
CONTEXT_EXECUTION_ID = 'execution_id'

# Step result keys
RESULT_SUCCESS = 'success'
RESULT_ERROR = 'error'
RESULT_TIME_MS = 'time_ms'
RESULT_DATA = 'data'
RESULT_EXTRACTED_DATA = 'extracted_data'
RESULT_ATTEMPT = 'attempt'

# Data keys for workflow results
DATA_KEY_POLICIES = 'policies'
DATA_KEY_POLICY_DETAILS = 'policy_details'
DATA_KEY_CLAIMS_DATA = 'claims_data'

# Error messages
ERROR_MAX_RETRIES_EXCEEDED = "Maximum retries exceeded"
ERROR_STEP_FAILED = "Step {} ({}) failed: {}"
ERROR_UNKNOWN_ERROR = "Unknown error"


class WorkflowConfigLoader:
    """Loads and manages workflow configurations from JSON files with enhanced registry support"""

    @staticmethod
    def load_workflow_config(workflow_type: str) -> Dict[str, Any]:
        """
        Load workflow configuration from registry system.
        Only V2 format is supported.
        """
        logger.debug(f"WorkflowConfigLoader.load_workflow_config: Loading V2 config for workflow_type={workflow_type}")

        registry = get_workflow_registry()

        # Map legacy workflow types to new workflow IDs for backward compatibility
        workflow_id_mapping = {
            'POLICY_LIST': 'bvtpa_policy_list',
            'POLICY_DETAILS': 'bvtpa_policy_details'
        }

        workflow_id = workflow_id_mapping.get(workflow_type, workflow_type)

        # Load from registry
        config = registry.get_workflow(workflow_id)
        if not config:
            logger.error(f"WorkflowConfigLoader.load_workflow_config: Workflow not found: {workflow_id}")
            raise PolicyWorkflowError(f"Workflow not found: {workflow_id}")

        logger.info(f"WorkflowConfigLoader.load_workflow_config: Loaded V2 config for {workflow_id}")
        return config

    @staticmethod
    def load_workflow_by_id(workflow_id: str) -> Dict[str, Any]:
        """Load workflow configuration by workflow ID from registry"""
        logger.debug(f"WorkflowConfigLoader.load_workflow_by_id: Loading config for workflow_id={workflow_id}")

        registry = get_workflow_registry()
        config = registry.get_workflow(workflow_id)

        if not config:
            logger.error(f"WorkflowConfigLoader.load_workflow_by_id: Workflow not found: {workflow_id}")
            raise PolicyWorkflowError(f"Workflow not found: {workflow_id}")

        logger.info(f"WorkflowConfigLoader.load_workflow_by_id: Successfully loaded config for {workflow_id}")
        return config

    @staticmethod
    def list_available_workflows() -> List[Dict[str, Any]]:
        """List all available workflows from registry"""
        registry = get_workflow_registry()
        workflows = registry.list_workflows(include_invalid=False)

        return [
            {
                'id': workflow.id,
                'name': workflow.name,
                'version': workflow.version,
                'description': workflow.description,
                'category': workflow.category,
                'schema_version': workflow.schema_version
            }
            for workflow in workflows
        ]

    @staticmethod
    def validate_workflow_config(workflow_id: str) -> ValidationResult:
        """Validate a workflow configuration"""
        registry = get_workflow_registry()
        return registry.validate_workflow(workflow_id)


class WorkflowTemplateResolver:
    """Resolves template variables in workflow configurations"""

    @staticmethod
    def resolve_template_variables(template: str, context: Dict[str, Any]) -> str:
        """Resolve template variables like {{variable_name}} in strings"""
        if not isinstance(template, str):
            return template

        # Find all template variables in the format {{variable_name}}
        matches = re.findall(TEMPLATE_VARIABLE_PATTERN, template)
        logger.debug(f"WorkflowTemplateResolver.resolve_template_variables: Found {len(matches)} template variables in: {template[:100]}...")

        resolved = template
        for match in matches:
            variable_name = match.strip()
            if variable_name in context:
                old_value = f'{{{{{variable_name}}}}}'
                new_value = str(context[variable_name])
                resolved = resolved.replace(old_value, new_value)
                logger.debug(f"WorkflowTemplateResolver.resolve_template_variables: Resolved {old_value} -> {new_value}")
            else:
                logger.warning(f"WorkflowTemplateResolver.resolve_template_variables: Template variable '{variable_name}' not found in context")

        return resolved

    @staticmethod
    def resolve_request_data(request_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve template variables in request data"""
        resolved = {}
        for key, value in request_data.items():
            if isinstance(value, str):
                resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
            else:
                resolved[key] = value
        return resolved

    @staticmethod
    def resolve_headers(headers: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Resolve template variables in headers"""
        resolved = {}
        for key, value in headers.items():
            resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
        return resolved


class WorkflowDataExtractor:
    """Extracts data from API responses using JSONPath-like expressions"""

    @staticmethod
    def extract_data(response: Any, extract_config: Dict[str, str]) -> Dict[str, Any]:
        """Extract data from response using extraction configuration"""
        logger.debug(f"WorkflowDataExtractor.extract_data: Starting data extraction with {len(extract_config) if extract_config else 0} extraction rules")

        if not extract_config:
            logger.debug("WorkflowDataExtractor.extract_data: No extraction configuration provided, returning empty dict")
            return {}

        extracted = {}
        for key, path in extract_config.items():
            try:
                logger.debug(f"WorkflowDataExtractor.extract_data: Extracting '{key}' using path '{path}'")
                value = WorkflowDataExtractor._extract_by_path(response, path)
                extracted[key] = value
                logger.debug(f"WorkflowDataExtractor.extract_data: Successfully extracted '{key}': {type(value).__name__} with length {len(value) if isinstance(value, (list, dict, str)) else 'N/A'}")
            except Exception as e:
                logger.warning(f"WorkflowDataExtractor.extract_data: Failed to extract '{key}' using path '{path}': {str(e)}")
                extracted[key] = None

        logger.info(f"WorkflowDataExtractor.extract_data: Completed extraction, extracted {len(extracted)} fields")
        return extracted

    @staticmethod
    def _extract_by_path(data: Any, path: str) -> Any:
        """Extract data using JSONPath-like syntax"""
        if path == '$':
            return data

        if not path.startswith('$.'):
            return None

        # Remove the '$.' prefix
        path_parts = path[2:].split('.')
        current = data

        for part in path_parts:
            if '[*]' in part:
                # Handle array extraction like 'ListOfPolicyListSocial[*].MemberCode'
                array_key = part.replace('[*]', '')
                if isinstance(current, dict) and array_key in current:
                    array_data = current[array_key]
                    if isinstance(array_data, list):
                        # Get the next part to extract from each array item
                        remaining_parts = path_parts[path_parts.index(part) + 1:]
                        if remaining_parts:
                            # Extract specific field from each array item
                            result = []
                            for item in array_data:
                                item_value = item
                                for remaining_part in remaining_parts:
                                    if isinstance(item_value, dict) and remaining_part in item_value:
                                        item_value = item_value[remaining_part]
                                    else:
                                        item_value = None
                                        break
                                if item_value is not None:
                                    result.append(item_value)
                            return result
                        else:
                            return array_data
                return None
            else:
                # Regular field access
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None

        return current


class WorkflowValidator:
    """Validates workflow step results"""

    @staticmethod
    def validate_response(response: Any, validation_rule: str) -> bool:
        """Validate response against validation rule"""
        logger.debug(f"WorkflowValidator.validate_response: Starting validation with rule: {validation_rule}")

        if not validation_rule:
            logger.debug("WorkflowValidator.validate_response: No validation rule provided, returning True")
            return True

        try:
            # Handle citizen ID verification
            if "Status == '1'" in validation_rule:
                logger.debug("WorkflowValidator.validate_response: Applying citizen ID status validation")
                result = WorkflowValidator._validate_citizen_id_status(response)
                logger.info(f"WorkflowValidator.validate_response: Citizen ID validation result: {result}")
                return result

            # Handle registration verification
            elif "Status == 'YES'" in validation_rule:
                logger.debug("WorkflowValidator.validate_response: Applying registration status validation")
                result = WorkflowValidator._validate_registration_status(response)
                logger.info(f"WorkflowValidator.validate_response: Registration validation result: {result}")
                return result

            else:
                logger.warning(f"WorkflowValidator.validate_response: Unknown validation rule: {validation_rule}")
                return True

        except Exception as e:
            logger.error(f"WorkflowValidator.validate_response: Validation failed with rule '{validation_rule}': {str(e)}")
            return False

    @staticmethod
    def _validate_citizen_id_status(response: Any) -> bool:
        """Validate citizen ID verification response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        search_results = response.get(FIELD_LIST_OF_SEARCH_CITIZEN_ID, [])
        if not isinstance(search_results, list) or len(search_results) == 0:
            raise ValueError("No citizen ID search results found")

        first_result = search_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid search result format")

        status = first_result.get(FIELD_STATUS)
        if status != '1':
            raise ValueError(f"Citizen ID verification failed: Status = {status}")

        return True

    @staticmethod
    def _validate_registration_status(response: Any) -> bool:
        """Validate registration check response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        check_results = response.get(FIELD_LIST_OF_CHECK_REGISTER, [])
        if not isinstance(check_results, list) or len(check_results) == 0:
            raise ValueError("No registration check results found")

        first_result = check_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid check result format")

        status = first_result.get(FIELD_STATUS)
        if status != 'YES':
            raise ValueError(f"Registration verification failed: Status = {status}")

        return True


class GenericWorkflowExecutor:
    """Generic workflow execution engine that processes JSON-defined workflows"""

    def __init__(self, tpa_service: TPAApiService):
        logger.debug("GenericWorkflowExecutor.__init__: Initializing V2 workflow executor")
        self.tpa_service = tpa_service
        logger.info("GenericWorkflowExecutor.__init__: V2 workflow executor initialized successfully")

    def execute_workflow(self, workflow_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete V2 workflow based on JSON configuration"""
        execution_id = context.get(CONTEXT_EXECUTION_ID, 'unknown')
        logger.info(f"GenericWorkflowExecutor.execute_workflow: Starting V2 workflow execution - type={workflow_type}, execution_id={execution_id}")

        config = WorkflowConfigLoader.load_workflow_config(workflow_type)

        # Ensure this is V2 format
        if config.get('schema_version') != '2.0':
            raise PolicyWorkflowError(f"Only V2 schema format is supported. Found: {config.get('schema_version')}")

        # Initialize execution context
        execution_context = {
            CONTEXT_STEP_DATA: {},
            CONTEXT_STEP_RESULTS: {},
            CONTEXT_TPA_CALLS: 0,
            CONTEXT_TPA_TIME: 0,
            **context
        }
        logger.debug(f"GenericWorkflowExecutor.execute_workflow: Initialized execution context with {len(context)} context items")

        # Resolve data source (database or fixed values)
        logger.debug("GenericWorkflowExecutor.execute_workflow: Resolving V2 data source")
        self._resolve_v2_data_source(config, execution_context)

        # Execute steps in dependency order
        steps = self._resolve_step_execution_order(config['steps'])
        logger.info(f"GenericWorkflowExecutor.execute_workflow: Executing {len(steps)} workflow steps in dependency order")

        for i, step in enumerate(steps, 1):
            step_name = step['name']
            step_id = step['id']

            logger.info(f"GenericWorkflowExecutor.execute_workflow: Executing step {i}/{len(steps)} - {step_id}:{step_name}")

            step_result = self._execute_v2_step_with_retry(step, execution_context)

            step_key = f"step_{step_id}_{step_name}"
            execution_context[CONTEXT_STEP_RESULTS][step_key] = step_result

            if not step_result[RESULT_SUCCESS]:
                error_msg = ERROR_STEP_FAILED.format(step_id, step_name, step_result.get(RESULT_ERROR, ERROR_UNKNOWN_ERROR))
                logger.error(f"GenericWorkflowExecutor.execute_workflow: Step failed - {error_msg}")
                raise PolicyWorkflowError(error_msg)

            # Store extracted data for subsequent steps
            if RESULT_EXTRACTED_DATA in step_result:
                extracted_count = len(step_result[RESULT_EXTRACTED_DATA])
                execution_context[CONTEXT_STEP_DATA].update(step_result[RESULT_EXTRACTED_DATA])
                logger.debug(f"GenericWorkflowExecutor.execute_workflow: Step {step_name} extracted {extracted_count} data fields")

        logger.info(f"GenericWorkflowExecutor.execute_workflow: V2 workflow completed successfully - type={workflow_type}, execution_id={execution_id}, total_tpa_calls={execution_context[CONTEXT_TPA_CALLS]}, total_tpa_time={execution_context[CONTEXT_TPA_TIME]:.2f}ms")
        return execution_context



    def _resolve_v2_data_source(self, config: Dict[str, Any], context: Dict[str, Any]):
        """Resolve data source for V2 configuration format"""
        configuration = config.get('configuration', {})
        data_source = configuration.get('data_source', {})

        mode = data_source.get('mode', 'database')

        if mode == 'database':
            # Use database queries from V2 configuration
            database_queries = data_source.get('database_queries', [])
            customer = context.get(CONTEXT_CUSTOMER)
            customer_id = context.get('customer_id') or (customer.customer_id if customer else None)
            platform_identity = context.get('platform_identity')
            platform_id = context.get('platform_id') or (platform_identity.id if platform_identity else None)

            if customer_id:
                resolved_data = {}
                for db_query in database_queries:
                    table_data = self._execute_v2_database_query(db_query, str(customer_id), str(platform_id) if platform_id else None)
                    resolved_data.update(table_data)
                logger.debug(f"GenericWorkflowExecutor._resolve_v2_data_source: Resolved database data: {resolved_data}")
            else:
                resolved_data = {}

            context[CONTEXT_STEP_DATA].update(resolved_data)

        elif mode == 'fixed':
            # Use fixed values from V2 configuration
            fixed_values = data_source.get('fixed_values', {})
            context[CONTEXT_STEP_DATA].update(fixed_values)

        elif mode == 'hybrid':
            # Use both database and fixed values
            # Database first, then fixed values as fallback/override
            database_queries = data_source.get('database_queries', [])
            fixed_values = data_source.get('fixed_values', {})

            # Start with fixed values
            context[CONTEXT_STEP_DATA].update(fixed_values)

            # Override with database values if available
            customer = context.get(CONTEXT_CUSTOMER)
            customer_id = context.get('customer_id') or (customer.customer_id if customer else None)
            platform_identity = context.get('platform_identity')
            platform_id = context.get('platform_id') or (platform_identity.id if platform_identity else None)

            if customer_id:
                resolved_data = {}
                for db_query in database_queries:
                    table_data = self._execute_v2_database_query(db_query, str(customer_id), str(platform_id) if platform_id else None)
                    resolved_data.update(table_data)
                context[CONTEXT_STEP_DATA].update(resolved_data)

        # Add member_code if provided in context
        if CONTEXT_MEMBER_CODE in context:
            context[CONTEXT_STEP_DATA][CONTEXT_MEMBER_CODE] = context[CONTEXT_MEMBER_CODE]

    def _execute_v2_database_query(self, db_config: Dict[str, Any], customer_id: str, platform_id: Optional[str] = None) -> Dict[str, Any]:
        """Execute database query for V2 configuration format"""
        from django.db import connection

        table = db_config['table']
        fields = db_config['fields']
        where_clause = db_config['where']
        is_required = db_config.get('required', False)

        # Prepare parameters list based on what's needed in the where clause
        params = []

        # Replace named parameters with Django's placeholder syntax and collect parameter values
        if ':customer_id' in where_clause:
            where_clause = where_clause.replace(':customer_id', '%s')
            params.append(customer_id)

        if ':platform_id' in where_clause:
            where_clause = where_clause.replace(':platform_id', '%s')
            if platform_id is None:
                if is_required:
                    logger.error(f"GenericWorkflowExecutor._execute_v2_database_query: platform_id required for query but not provided")
                    raise PolicyWorkflowError("Platform ID required for database query but not provided")
                else:
                    logger.warning(f"GenericWorkflowExecutor._execute_v2_database_query: platform_id not provided for optional query")
                    return {}
            params.append(platform_id)

        # Build SQL query
        field_list = ', '.join(fields.values())
        sql = f"SELECT {field_list} FROM {table} WHERE {where_clause}"

        logger.debug(f"GenericWorkflowExecutor._execute_v2_database_query: Executing SQL: {sql} with params: {params}")

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                row = cursor.fetchone()

                if row:
                    # Map database fields to workflow variables
                    result = {}
                    for workflow_var, db_field in fields.items():
                        field_index = list(fields.values()).index(db_field)
                        result[workflow_var] = row[field_index]
                    logger.debug(f"GenericWorkflowExecutor._execute_v2_database_query: Query returned data: {result}")
                    return result
                else:
                    if is_required:
                        logger.error(f"GenericWorkflowExecutor._execute_v2_database_query: Required data not found for table {table} with params {params}")
                        raise PolicyWorkflowError(f"Required data not found in {table}")
                    else:
                        logger.warning(f"GenericWorkflowExecutor._execute_v2_database_query: No data found for table {table} with params {params}")

        except Exception as e:
            if is_required:
                logger.error(f"GenericWorkflowExecutor._execute_v2_database_query: Database query failed: {str(e)}")
                raise PolicyWorkflowError(f"Database query failed: {str(e)}")
            else:
                logger.warning(f"GenericWorkflowExecutor._execute_v2_database_query: Optional database query failed: {str(e)}")

        return {}

    def _resolve_step_execution_order(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Resolve step execution order based on dependencies"""
        # Create a mapping of step IDs to steps
        step_map = {step['id']: step for step in steps}

        # Topological sort to resolve dependencies
        visited = set()
        temp_visited = set()
        ordered_steps = []

        def visit(step_id: str):
            if step_id in temp_visited:
                raise PolicyWorkflowError(f"Circular dependency detected involving step: {step_id}")
            if step_id in visited:
                return

            temp_visited.add(step_id)

            step = step_map.get(step_id)
            if step:
                depends_on = step.get('depends_on', [])
                for dependency in depends_on:
                    visit(dependency)

                visited.add(step_id)
                ordered_steps.append(step)

            temp_visited.remove(step_id)

        # Visit all steps
        for step in steps:
            if step['id'] not in visited:
                visit(step['id'])

        logger.debug(f"GenericWorkflowExecutor._resolve_step_execution_order: Resolved execution order: {[s['id'] for s in ordered_steps]}")
        return ordered_steps

    def _execute_v2_step_with_retry(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a V2 step with retry logic"""
        step_name = step['name']
        step_id = step['id']
        step_config = step.get('config', {})
        max_retries = step_config.get('retry', {}).get('max_attempts', DEFAULT_MAX_RETRIES)
        execution_id = context.get(CONTEXT_EXECUTION_ID, 'unknown')

        logger.debug(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Starting V2 step execution - step_id={step_id}, step_name={step_name}, max_retries={max_retries}, execution_id={execution_id}")

        for attempt in range(max_retries):
            try:
                logger.debug(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Attempt {attempt + 1}/{max_retries} for step {step_name}")
                start_time = time.time()

                # Execute V2 step
                response = self._execute_v2_step(step, context)
                execution_time = (time.time() - start_time) * 1000
                logger.info(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Step {step_name} executed successfully in {execution_time:.2f}ms")

                # Extract data from response
                extracted_data = {}
                response_extraction = step_config.get('response_extraction', {})
                if response_extraction:
                    logger.debug(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Extracting data from V2 step {step_name} response")
                    extracted_data = self._extract_v2_response_data(response, response_extraction)

                # Validate response if validation rule exists
                validation_config = step_config.get('validation', {})
                if validation_config:
                    logger.debug(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Validating V2 response for step {step_name}")
                    self._validate_v2_response(response, validation_config, context)

                # Update context counters
                context[CONTEXT_TPA_CALLS] += 1
                context[CONTEXT_TPA_TIME] += execution_time

                logger.info(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Step {step_name} completed successfully on attempt {attempt + 1}")
                return {
                    RESULT_SUCCESS: True,
                    RESULT_TIME_MS: execution_time,
                    RESULT_DATA: response,
                    RESULT_EXTRACTED_DATA: extracted_data,
                    RESULT_ATTEMPT: attempt + 1
                }

            except Exception as e:
                logger.warning(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Step {step_name} attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    # Last attempt failed
                    logger.error(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Step {step_name} failed after {max_retries} attempts: {str(e)}")
                    return {
                        RESULT_SUCCESS: False,
                        RESULT_ERROR: str(e),
                        RESULT_ATTEMPT: attempt + 1
                    }
                else:
                    # Wait before retry
                    retry_delay = step_config.get('retry', {}).get('delay_seconds', DEFAULT_RETRY_DELAY_SECONDS)
                    backoff_delay = retry_delay * (2 ** attempt)
                    logger.debug(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Waiting {backoff_delay}s before retry for step {step_name}")
                    time.sleep(backoff_delay)  # Exponential backoff

        # This should never be reached, but just in case
        logger.error(f"GenericWorkflowExecutor._execute_v2_step_with_retry: Step {step_name} exceeded maximum retries")
        return {
            RESULT_SUCCESS: False,
            RESULT_ERROR: ERROR_MAX_RETRIES_EXCEEDED,
            RESULT_ATTEMPT: max_retries
        }







    def _execute_v2_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute a V2 format step based on its type"""
        step_type = step.get('type', 'http_request')
        step_config = step.get('config', {})

        if step_type == 'http_request':
            return self._execute_v2_http_request(step_config, context)
        else:
            # For now, only HTTP requests are supported
            logger.warning(f"GenericWorkflowExecutor._execute_v2_step: Unsupported step type '{step_type}', treating as http_request")
            return self._execute_v2_http_request(step_config, context)

    def _execute_v2_http_request(self, config: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute V2 HTTP request step"""
        endpoint = WorkflowTemplateResolver.resolve_template_variables(config['endpoint'], context[CONTEXT_STEP_DATA])
        method = config['method']
        headers = WorkflowTemplateResolver.resolve_headers(config.get('headers', {}), context[CONTEXT_STEP_DATA])
        request_body = WorkflowTemplateResolver.resolve_request_data(config.get('request_body', {}), context[CONTEXT_STEP_DATA])

        # Log the resolved request details for debugging
        safe_headers = {k: ('***' if 'authorization' in k.lower() else v) for k, v in headers.items()}
        safe_body = {k: ('***' if k in ['PASSWORD'] else v) for k, v in request_body.items()} if isinstance(request_body, dict) else request_body
        logger.info(f"GenericWorkflowExecutor._execute_v2_http_request: Making request - Endpoint: {endpoint}, Method: {method}")
        logger.debug(f"GenericWorkflowExecutor._execute_v2_http_request: Headers: {safe_headers}")
        logger.debug(f"GenericWorkflowExecutor._execute_v2_http_request: Body: {safe_body}")

        return self.tpa_service.make_dynamic_request(endpoint, method, request_body, headers)



    def _extract_v2_response_data(self, response: Any, extraction_config: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Extract data from response using V2 extraction configuration"""
        extracted = {}

        for key, config in extraction_config.items():
            try:
                path = config['path']
                value = WorkflowDataExtractor._extract_by_path(response, path)

                # Apply type conversion if specified
                data_type = config.get('type', 'auto')
                if data_type == 'string' and value is not None:
                    value = str(value)
                elif data_type == 'integer' and value is not None:
                    value = int(value)
                elif data_type == 'array' and not isinstance(value, list):
                    value = [value] if value is not None else []

                extracted[key] = value
                logger.debug(f"GenericWorkflowExecutor._extract_v2_response_data: Extracted '{key}': {type(value).__name__}")

            except Exception as e:
                is_required = config.get('required', False)
                if is_required:
                    logger.error(f"GenericWorkflowExecutor._extract_v2_response_data: Failed to extract required field '{key}': {str(e)}")
                    raise PolicyWorkflowError(f"Failed to extract required field '{key}': {str(e)}")
                else:
                    logger.warning(f"GenericWorkflowExecutor._extract_v2_response_data: Failed to extract optional field '{key}': {str(e)}")
                    extracted[key] = None

        return extracted

    def _validate_v2_response(self, response: Any, validation_config: Dict[str, Any], context: Dict[str, Any]):
        """Validate response using V2 validation configuration"""
        # Note: context parameter reserved for future use
        rules = validation_config.get('rules', [])

        # Log the full response being validated for debugging
        logger.info(f"GenericWorkflowExecutor._validate_v2_response: Validating response: {json.dumps(response, indent=2, ensure_ascii=False) if isinstance(response, (dict, list)) else str(response)}")

        for rule in rules:
            try:
                rule_type = rule.get('type')

                if rule_type == 'json_path':
                    path = rule['path']
                    operator = rule['operator']
                    expected_value = rule.get('value')

                    actual_value = WorkflowDataExtractor._extract_by_path(response, path)

                    # Log detailed validation information for debugging
                    logger.info(f"GenericWorkflowExecutor._validate_v2_response: Validating rule {rule.get('id', 'unknown')} - Path: {path}, Expected: {expected_value}, Actual: {actual_value}, Operator: {operator}")

                    is_valid = self._apply_validation_operator(actual_value, operator, expected_value)

                    if not is_valid:
                        error_message = rule.get('error_message', f"Validation failed for rule {rule.get('id', 'unknown')}")
                        warning_only = rule.get('warning_only', False)

                        # Enhanced error logging with actual vs expected values
                        detailed_error = f"{error_message} (Expected: {expected_value}, Actual: {actual_value}, Path: {path})"

                        if warning_only:
                            logger.warning(f"GenericWorkflowExecutor._validate_v2_response: Validation warning: {detailed_error}")
                        else:
                            logger.error(f"GenericWorkflowExecutor._validate_v2_response: Validation error: {detailed_error}")
                            raise PolicyWorkflowError(error_message)
                    else:
                        logger.debug(f"GenericWorkflowExecutor._validate_v2_response: Validation passed for rule {rule.get('id', 'unknown')}")

                elif rule_type == 'custom_function':
                    # Custom function validation would be implemented here
                    logger.warning(f"GenericWorkflowExecutor._validate_v2_response: Custom function validation not yet implemented")

            except PolicyWorkflowError:
                raise  # Re-raise validation errors
            except Exception as e:
                logger.error(f"GenericWorkflowExecutor._validate_v2_response: Validation rule processing failed: {str(e)}")
                raise PolicyWorkflowError(f"Validation rule processing failed: {str(e)}")

    def _apply_validation_operator(self, actual_value: Any, operator: str, expected_value: Any) -> bool:
        """Apply validation operator to compare actual and expected values"""
        try:
            if operator == 'equals':
                return actual_value == expected_value
            elif operator == 'not_equals':
                return actual_value != expected_value
            elif operator == 'contains':
                return expected_value in str(actual_value) if actual_value is not None else False
            elif operator == 'not_contains':
                return expected_value not in str(actual_value) if actual_value is not None else True
            elif operator == 'greater_than':
                return float(actual_value) > float(expected_value)
            elif operator == 'less_than':
                return float(actual_value) < float(expected_value)
            elif operator == 'not_empty':
                if isinstance(actual_value, (list, dict, str)):
                    return len(actual_value) > 0
                return actual_value is not None
            else:
                logger.warning(f"GenericWorkflowExecutor._apply_validation_operator: Unknown operator: {operator}")
                return True
        except (ValueError, TypeError) as e:
            logger.error(f"GenericWorkflowExecutor._apply_validation_operator: Operator application failed: {str(e)}")
            return False





class PolicyWorkflowService:
    """Service for executing policy workflows with caching and audit logging"""

    CACHE_DURATION_MINUTES = DEFAULT_CACHE_DURATION_MINUTES

    @classmethod
    def execute_policy_list_workflow_by_platform_id(cls, platform_id: int, user: User) -> Dict[str, Any]:
        """
        Execute policy list workflow using platformId parameter from frontend.
        This method handles the platformId lookup and extracts the required platform identity data.
        """
        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow_by_platform_id: Starting workflow with platform_id={platform_id}, user={user.username}")

        # Get platform identity data using platformId
        platform_data = cls.get_platform_identity_data(platform_id)
        customer_id = platform_data['customer_id']

        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow_by_platform_id: Resolved platform_id={platform_id} to customer_id={customer_id}, platform={platform_data['platform']}, user_id={platform_data['platform_user_id']}, channel_id={platform_data['channel_id']}")

        # Execute the workflow with the resolved customer_id and platform_id
        return cls.execute_policy_list_workflow(customer_id, platform_id, user)

    @classmethod
    def execute_policy_details_workflow_by_platform_id(cls, platform_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """
        Execute policy details workflow using platformId parameter from frontend.
        This method handles the platformId lookup and extracts the required platform identity data.
        """
        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow_by_platform_id: Starting workflow with platform_id={platform_id}, member_code={member_code}, user={user.username}")

        # Get platform identity data using platformId
        platform_data = cls.get_platform_identity_data(platform_id)
        customer_id = platform_data['customer_id']

        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow_by_platform_id: Resolved platform_id={platform_id} to customer_id={customer_id}, platform={platform_data['platform']}, user_id={platform_data['platform_user_id']}, channel_id={platform_data['channel_id']}")

        # Execute the workflow with the resolved customer_id and platform_id
        return cls.execute_policy_details_workflow(customer_id, platform_id, member_code, user)
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, platform_id: int, user: User) -> Dict[str, Any]:
        """Execute complete policy list workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Starting policy list workflow - customer_id={customer_id}, platform_id={platform_id}, user={user.username}, execution_id={execution_id}")

        # Validate input parameters
        if not customer_id or customer_id <= 0:
            raise PolicyWorkflowError("Invalid customer_id provided")
        if not platform_id or platform_id <= 0:
            raise PolicyWorkflowError("Invalid platform_id provided")

        try:
            # Get customer and platform identity
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Retrieving customer and platform identity for customer_id={customer_id}, platform_id={platform_id}")
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity_by_id(customer, platform_id)
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}, channel_id={platform_identity.channel_id}")

            # Check cache first
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Checking cache for customer {customer_id}, platform_id={platform_id}")
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_LIST, platform_identity)
            if cached_result:
                logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Returning cached policy list for customer {customer_id}, platform_id={platform_id}, execution_id={cached_result.get(CONTEXT_EXECUTION_ID, 'unknown')}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: No cache found, executing workflow for customer {customer_id}, platform_id={platform_id}")

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'customer_id': customer_id,
                'platform_identity': platform_identity,
                'platform_id': platform_id,
                CONTEXT_EXECUTION_ID: execution_id
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_LIST, context)

            # Extract workflow data
            policy_list_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICIES, [])
            raw_response = {FIELD_LIST_OF_POLICY_LIST_SOCIAL: policy_list_data}
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Extracted {len(policy_list_data)} policies from workflow")

            # Process and format data
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Processing policy list data")
            processed_data = cls._process_policy_list_data(raw_response)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Caching result for customer {customer_id}, platform_id={platform_id}")
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_LIST, None, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Successfully completed policy list workflow - customer_id={customer_id}, platform_id={platform_id}, execution_id={execution_id}, total_time={total_time:.2f}ms, policies_count={len(policy_list_data)}")
            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            try:
                cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, step_results,
                              total_time, False, error_details, tpa_calls, tpa_time)
            except Exception as audit_error:
                logger.error(f"PolicyWorkflowService.execute_policy_list_workflow: Failed to log audit for failed workflow: {str(audit_error)}")

            logger.error(f"PolicyWorkflowService.execute_policy_list_workflow: Policy list workflow failed for customer {customer_id}, platform_id={platform_id}, execution_id={execution_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy list workflow failed: {str(e)}")
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, platform_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """Execute complete policy details workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Starting policy details workflow - customer_id={customer_id}, platform_id={platform_id}, member_code={member_code}, user={user.username}, execution_id={execution_id}")

        # Validate input parameters
        if not customer_id or customer_id <= 0:
            raise PolicyWorkflowError("Invalid customer_id provided")
        if not platform_id or platform_id <= 0:
            raise PolicyWorkflowError("Invalid platform_id provided")
        if not member_code or not member_code.strip():
            raise PolicyWorkflowError("Invalid member_code provided")

        try:
            # Get customer and platform identity
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Retrieving customer and platform identity for customer_id={customer_id}, platform_id={platform_id}")
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity_by_id(customer, platform_id)
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}, channel_id={platform_identity.channel_id}")

            # Check cache first
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Checking cache for customer {customer_id}, platform_id={platform_id}, member {member_code}")
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, platform_identity, member_code)
            if cached_result:
                logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Returning cached policy details for customer {customer_id}, platform_id={platform_id}, member {member_code}, execution_id={cached_result.get(CONTEXT_EXECUTION_ID, 'unknown')}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: No cache found, executing workflow for customer {customer_id}, platform_id={platform_id}, member {member_code}")

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'customer_id': customer_id,
                'platform_identity': platform_identity,
                'platform_id': platform_id,
                CONTEXT_EXECUTION_ID: execution_id,
                CONTEXT_MEMBER_CODE: member_code
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_DETAILS, context)

            # Extract workflow data
            policy_details = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICY_DETAILS, [])
            claims_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_CLAIMS_DATA, [])
            raw_response = {
                FIELD_LIST_OF_POL_DET: policy_details,
                FIELD_LIST_OF_POL_CLAIM: claims_data
            }
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Extracted {len(policy_details)} policy details and {len(claims_data)} claims from workflow")

            # Process and format data
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Processing policy details data")
            processed_data = cls._process_policy_details_data(raw_response, member_code)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Caching result for customer {customer_id}, platform_id={platform_id}, member {member_code}")
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, member_code, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Successfully completed policy details workflow - customer_id={customer_id}, platform_id={platform_id}, member_code={member_code}, execution_id={execution_id}, total_time={total_time:.2f}ms, policy_details_count={len(policy_details)}, claims_count={len(claims_data)}")
            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            try:
                cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, step_results,
                              total_time, False, error_details, tpa_calls, tpa_time)
            except Exception as audit_error:
                logger.error(f"PolicyWorkflowService.execute_policy_details_workflow: Failed to log audit for failed workflow: {str(audit_error)}")

            logger.error(f"PolicyWorkflowService.execute_policy_details_workflow: Policy details workflow failed for customer {customer_id}, platform_id={platform_id}, member {member_code}, execution_id={execution_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy details workflow failed: {str(e)}")
    
    @classmethod
    def _get_platform_identity(cls, customer: Customer) -> CustomerPlatformIdentity:
        """Get platform identity for customer using default platform"""
        platform_identity = customer.get_identity_for_platform('LINE')
        if not platform_identity:
            raise CustomerDataError(f"No LINE platform identity found for customer {customer.customer_id}")
        return platform_identity

    @classmethod
    def get_platform_identity_data(cls, platform_id: int) -> Dict[str, Any]:
        """
        Get platform identity data by platformId parameter from frontend.
        Queries customer_customerplatformidentity table and extracts:
        - platform_user_id
        - channel_id
        - platform
        """
        logger.debug(f"PolicyWorkflowService.get_platform_identity_data: Looking up platform_id={platform_id}")

        try:
            platform_identity = CustomerPlatformIdentity.objects.get(
                id=platform_id,
                is_active=True
            )

            result = {
                'platform_user_id': platform_identity.platform_user_id,
                'channel_id': platform_identity.channel_id,
                'platform': platform_identity.platform,
                'customer_id': platform_identity.customer.customer_id,
                'display_name': platform_identity.display_name,
                'provider_id': platform_identity.provider_id,
                'provider_name': platform_identity.provider_name,
                'channel_name': platform_identity.channel_name
            }

            logger.info(f"PolicyWorkflowService.get_platform_identity_data: Found platform identity - platform={result['platform']}, user_id={result['platform_user_id']}, channel_id={result['channel_id']}, customer_id={result['customer_id']}")
            return result

        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"PolicyWorkflowService.get_platform_identity_data: Platform identity with id={platform_id} not found")
            raise CustomerDataError(f"Platform identity with id={platform_id} not found")
        except Exception as e:
            logger.error(f"PolicyWorkflowService.get_platform_identity_data: Error retrieving platform identity: {str(e)}")
            raise CustomerDataError(f"Error retrieving platform identity: {str(e)}")

    @classmethod
    def _get_platform_identity_by_id(cls, customer: Customer, platform_id: int) -> CustomerPlatformIdentity:
        """Get specific platform identity by ID for customer"""
        logger.debug(f"PolicyWorkflowService._get_platform_identity_by_id: Looking for platform_id={platform_id} for customer_id={customer.customer_id}")

        try:
            platform_identity = CustomerPlatformIdentity.objects.get(
                id=platform_id,
                customer=customer,
                is_active=True
            )
            logger.debug(f"PolicyWorkflowService._get_platform_identity_by_id: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}")
            return platform_identity
        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"PolicyWorkflowService._get_platform_identity_by_id: Platform identity with id={platform_id} not found for customer {customer.customer_id}")
            raise CustomerDataError(f"Platform identity with id={platform_id} not found for customer {customer.customer_id}")
        except Exception as e:
            logger.error(f"PolicyWorkflowService._get_platform_identity_by_id: Error retrieving platform identity: {str(e)}")
            raise CustomerDataError(f"Error retrieving platform identity: {str(e)}")

    @classmethod
    def _get_cached_result(cls, customer: Customer, workflow_type: str, platform_identity: CustomerPlatformIdentity, member_code: Optional[str] = None) -> Optional[Dict]:
        """Get cached workflow result if available and not expired"""
        logger.debug(f"PolicyWorkflowService._get_cached_result: Checking cache for customer_id={customer.customer_id}, platform_id={platform_identity.pk}, workflow_type={workflow_type}, member_code={member_code}")

        try:
            cache_entry = CustomerPolicyWorkflowCache.objects.get(
                customer=customer,
                platform_identity=platform_identity,
                workflow_type=workflow_type,
                member_code=member_code,
                expires_at__gt=timezone.now()
            )
            logger.info(f"PolicyWorkflowService._get_cached_result: Found valid cache entry for customer_id={customer.customer_id}, platform_id={platform_identity.pk}, workflow_type={workflow_type}, member_code={member_code}, execution_id={cache_entry.execution_id}")
            return {
                CONTEXT_PROCESSED_DATA: cache_entry.processed_data,
                CONTEXT_EXECUTION_ID: cache_entry.execution_id
            }
        except CustomerPolicyWorkflowCache.DoesNotExist:
            logger.debug(f"PolicyWorkflowService._get_cached_result: No valid cache entry found for customer_id={customer.customer_id}, platform_id={platform_identity.pk}, workflow_type={workflow_type}, member_code={member_code}")
            return None

    @classmethod
    def _cache_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str],
                     platform_identity: CustomerPlatformIdentity, raw_data: Dict, processed_data: Dict,
                     execution_id: str, execution_time: float):
        """Cache workflow result"""
        expires_at = timezone.now() + timedelta(minutes=cls.CACHE_DURATION_MINUTES)
        logger.debug(f"PolicyWorkflowService._cache_result: Caching result for customer_id={customer.customer_id}, platform_id={platform_identity.pk}, workflow_type={workflow_type}, member_code={member_code}, execution_id={execution_id}, expires_at={expires_at}")

        with transaction.atomic():
            # Delete existing cache entry if exists (now includes platform_identity)
            deleted_count, _ = CustomerPolicyWorkflowCache.objects.filter(
                customer=customer,
                platform_identity=platform_identity,
                workflow_type=workflow_type,
                member_code=member_code
            ).delete()

            if deleted_count > 0:
                logger.debug(f"PolicyWorkflowService._cache_result: Deleted {deleted_count} existing cache entries for platform_id={platform_identity.pk}")

            # Create new cache entry
            CustomerPolicyWorkflowCache.objects.create(
                customer=customer,
                platform_identity=platform_identity,
                workflow_type=workflow_type,
                member_code=member_code,
                citizen_id=customer.national_id,
                social_id=platform_identity.platform_user_id,
                channel_id=platform_identity.channel_id,
                channel=platform_identity.platform,
                raw_response_data=raw_data,
                processed_data=processed_data,
                execution_id=execution_id,
                execution_time_ms=int(execution_time),
                success=True,
                expires_at=expires_at
            )
            logger.info(f"PolicyWorkflowService._cache_result: Successfully cached result for customer_id={customer.customer_id}, platform_id={platform_identity.pk}, workflow_type={workflow_type}, member_code={member_code}, execution_id={execution_id}")

    @classmethod
    def clear_cache_for_platform_identity(cls, platform_identity: CustomerPlatformIdentity, workflow_type: Optional[str] = None) -> int:
        """
        Clear cache entries for a specific platform identity.
        Optionally filter by workflow_type.
        Returns the number of cache entries deleted.
        """
        logger.debug(f"PolicyWorkflowService.clear_cache_for_platform_identity: Clearing cache for platform_id={platform_identity.pk}, workflow_type={workflow_type}")

        filter_kwargs = {
            'platform_identity': platform_identity
        }

        if workflow_type:
            filter_kwargs['workflow_type'] = workflow_type

        deleted_count, _ = CustomerPolicyWorkflowCache.objects.filter(**filter_kwargs).delete()

        logger.info(f"PolicyWorkflowService.clear_cache_for_platform_identity: Cleared {deleted_count} cache entries for platform_id={platform_identity.pk}, workflow_type={workflow_type}")
        return deleted_count

    @classmethod
    def clear_expired_cache_entries(cls) -> int:
        """
        Clear all expired cache entries.
        Returns the number of cache entries deleted.
        """
        logger.debug("PolicyWorkflowService.clear_expired_cache_entries: Clearing expired cache entries")

        deleted_count, _ = CustomerPolicyWorkflowCache.objects.filter(
            expires_at__lte=timezone.now()
        ).delete()

        logger.info(f"PolicyWorkflowService.clear_expired_cache_entries: Cleared {deleted_count} expired cache entries")
        return deleted_count

    @classmethod
    def get_cache_stats_for_customer(cls, customer: Customer) -> Dict[str, Any]:
        """
        Get cache statistics for a specific customer.
        Returns information about cache entries across all platform identities.
        """
        logger.debug(f"PolicyWorkflowService.get_cache_stats_for_customer: Getting cache stats for customer_id={customer.customer_id}")

        cache_entries = CustomerPolicyWorkflowCache.objects.filter(customer=customer)

        stats = {
            'total_entries': cache_entries.count(),
            'active_entries': cache_entries.filter(expires_at__gt=timezone.now()).count(),
            'expired_entries': cache_entries.filter(expires_at__lte=timezone.now()).count(),
            'by_workflow_type': {},
            'by_platform_identity': {}
        }

        # Group by workflow type
        for workflow_type in ['POLICY_LIST', 'POLICY_DETAILS']:
            type_entries = cache_entries.filter(workflow_type=workflow_type)
            stats['by_workflow_type'][workflow_type] = {
                'total': type_entries.count(),
                'active': type_entries.filter(expires_at__gt=timezone.now()).count()
            }

        # Group by platform identity
        platform_identities = cache_entries.values_list('platform_identity', flat=True).distinct()
        for platform_id in platform_identities:
            if platform_id:
                platform_entries = cache_entries.filter(platform_identity_id=platform_id)
                stats['by_platform_identity'][platform_id] = {
                    'total': platform_entries.count(),
                    'active': platform_entries.filter(expires_at__gt=timezone.now()).count()
                }

        logger.debug(f"PolicyWorkflowService.get_cache_stats_for_customer: Cache stats for customer_id={customer.customer_id}: {stats}")
        return stats

    @classmethod
    def _log_audit(cls, customer: Customer, user: User, workflow_type: str, execution_id: str,
                  step_results: Dict, total_time: float, success: bool, error_details: Optional[Dict],
                  tpa_calls: int, tpa_time: float):
        """Log workflow execution audit"""
        CustomerPolicyWorkflowAuditLog.objects.create(
            customer=customer,
            requested_by=user,
            workflow_type=workflow_type,
            execution_id=execution_id,
            step_results=step_results,
            total_execution_time_ms=int(total_time),
            success=success,
            error_details=error_details,
            tpa_calls_made=tpa_calls,
            tpa_total_time_ms=int(tpa_time)
        )

    @classmethod
    def _process_policy_list_data(cls, raw_data: Dict) -> Dict[str, Any]:
        """Process and format policy list data for frontend"""
        policy_list = raw_data.get(FIELD_LIST_OF_POLICY_LIST_SOCIAL, [])

        # Extract member codes
        member_codes = []
        for policy in policy_list:
            member_code = policy.get(FIELD_MEMBER_CODE)
            if member_code and member_code not in member_codes:
                member_codes.append(member_code)

        return {
            'policy_list_data': raw_data,
            'member_codes': member_codes,
            'execution_metadata': {
                'total_policies': len(policy_list),
                'unique_member_codes': len(member_codes),
                'processed_at': timezone.now().isoformat()
            }
        }

    @classmethod
    def _process_policy_details_data(cls, raw_data: Dict, member_code: str) -> Dict[str, Any]:
        """Process and format policy details data for frontend"""
        policy_details = raw_data.get(FIELD_LIST_OF_POL_DET, [])
        policy_claims = raw_data.get(FIELD_LIST_OF_POL_CLAIM, [])

        return {
            'policy_details_data': raw_data,
            CONTEXT_MEMBER_CODE: member_code,
            'execution_metadata': {
                'total_policy_details': len(policy_details),
                'total_claims': len(policy_claims),
                'processed_at': timezone.now().isoformat()
            }
        }
