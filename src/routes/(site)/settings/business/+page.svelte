<!-- $/src/routes/(site)/settings/business/+page.svelte -->
<script lang="ts">
    import type { PageData } from '../business/$types';
    import { writable } from 'svelte/store';
    import { onMount, afterUpdate } from 'svelte';
    import { Input, Label, Button, Toggle, Secondary } from 'flowbite-svelte';
    import {
        CloseCircleSolid,
        CheckCircleSolid
    } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n';

    import { enhance } from '$app/forms';
    import { Toast } from 'flowbite-svelte';
    import { fly } from 'svelte/transition';  
    // import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { getSubscriptionFromStorage } from '$lib/stores/subscriptionStatus';
    import { goto } from '$app/navigation';
    
    import CompanySection from '$src/lib/components/settings/business/CompanySection.svelte';
    import ChatbotSection from '$src/lib/components/settings/business/ChatbotSection.svelte';
    import ConnectionSection from '$src/lib/components/settings/business/ConnectionSection.svelte';
    import WebsiteSection from '$src/lib/components/settings/business/Systemection.svelte';
    import ProductSection from '$src/lib/components/settings/business/ProductSection.svelte';
    // import PolicyClaimsApiStepsSection from '$src/lib/components/settings/business/PolicyClaimsApiStepsSection.svelte';
    // import UserSection from '$src/lib/components/settings/business/UserSection.svelte';
    // import CustomerSection from '$src/lib/components/settings/business/CustomerSection.svelte';  
    // import TicketSection from '$src/lib/components/settings/business/TicketSection.svelte';

    export let data: PageData;
    
    // Store original data for reference
    let originalData = structuredClone(data);
    
    // Toast state for feedback
    let toastMessage = 'Successfully';
    let toastStatus = false;
    let counter = 3;
    
    // Active tab with persistence - initialize as empty string
    let activeTab = '';
    let pageTitle = "Business";
    let lastActiveTab = '';
    let settingsForm;
    
    // Available tabs configuration
    const availableTabs = ['system', 'company', 'connection', 'bot', 'product', 'api-steps'];
    const defaultTab = 'system';
    const TAB_STORAGE_KEY = 'business_settings_active_tab';
    
    // Create stores for each section with proper initialization
    const systemSettings = writable({
        logo: data.system_setting.COMPANY_LOGO,
        dominant_color: data.system_setting.DOMINANT_COLOR,
        secondary_color: data.system_setting.SECONDARY_COLOR,
        accent_color: data.system_setting.ACCENT_COLOR,
        inactive_1st: data.system_setting.INACTIVE_TICKET_1ST_TIME_MINUTES,
        inactive_2nd: data.system_setting.INACTIVE_TICKET_2ND_TIME_MINUTES,
        csat_image : data.system_setting.LINE_CSAT
    });

    const botSettings = writable({
        thaiName: data.system_setting.CHATBOT_MASCOT_THAI_NAME,
        englishName: data.system_setting.CHATBOT_MASCOT_ENGLISH_NAME,
        role: data.system_setting.CHATBOT_ROLE,
        gender: data.system_setting.CHATBOT_GENDER,
        conversationStyle: data.system_setting.CHATBOT_CONVERSATION_STYLE,
        conversationType: data.system_setting.CHATBOT_CONVERSATION_TYPE,
    });

    const companySettings = writable({
        thaiName: data.system_setting.COMPANY_THAI_NAME,
        englishName: data.system_setting.COMPANY_ENGLISH_NAME,
        business: data.system_setting.COMPANY_BUSINESS,
        businessType: data.system_setting.COMPANY_BUSINESS_TYPE,
    });

    const connectionSettings = writable({
        lineChannelSecret: data.system_setting.LINE_CHANNEL_SECRET,
        lineAccessToken: data.system_setting.LINE_ACCESS_TOKEN,
        lineWebhook: data.system_setting.LINE_WEBHOOK,
        lineOAQRCode: data.system_setting.LINE_OA_QR_CODE,
        lineOAQRLink: data.system_setting.LINE_OA_QR_LINK,
        lineGroupQRCode: data.system_setting.LINE_GROUP_QR_CODE,
        lineGroupQRLink: data.system_setting.LINE_GROUP_QR_LINK
    });
    
    const transferSettings = writable({
        transfer_ticket_partner: data.system_setting.AUTO_TRANSFER_TICKET_PARTNER_CONDITION,
        transfer_ticket_department: data.system_setting.AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION,
        transfer_ticket_tag: data.system_setting.AUTO_TRANSFER_TICKET_USER_TAG_CONDITION
    });
    
    // Reactive declarations for filtered data
    $: productList = {};
    
    // Function to save active tab to storage
    function saveActiveTab(tab) {
        if (typeof window !== 'undefined') {
            try {
                window.sessionStorage.setItem(TAB_STORAGE_KEY, tab);
            } catch (e) {
                console.warn('Failed to save active tab to sessionStorage:', e);
            }
        }
    }
    
    // Function to load active tab from storage
    function loadActiveTab() {
        if (typeof window !== 'undefined') {
            try {
                const savedTab = window.sessionStorage.getItem(TAB_STORAGE_KEY);
                return savedTab && availableTabs.includes(savedTab) ? savedTab : defaultTab;
            } catch (e) {
                console.warn('Failed to load active tab from sessionStorage:', e);
                return defaultTab;
            }
        }
        return defaultTab;
    }
    
    // Function to initialize active tab
    function initializeActiveTab() {
        const savedTab = loadActiveTab();
        activeTab = savedTab;
        // console.log('Initialized active tab:', activeTab);
    }
    
    // Function to refresh all stores from data
    function refreshAllStores() {
        // console.log("Refreshing all stores with latest data");
        
        systemSettings.set({
            logo: data.system_setting.COMPANY_LOGO,
            dominant_color: data.system_setting.DOMINANT_COLOR,
            secondary_color: data.system_setting.SECONDARY_COLOR,
            accent_color: data.system_setting.ACCENT_COLOR,
            inactive_1st: data.system_setting.INACTIVE_TICKET_1ST_TIME_MINUTES,
            inactive_2nd: data.system_setting.INACTIVE_TICKET_2ND_TIME_MINUTES,
            csat_image : data.system_setting.LINE_CSAT
        });
        
        botSettings.set({
            thaiName: data.system_setting.CHATBOT_MASCOT_THAI_NAME,
            englishName: data.system_setting.CHATBOT_MASCOT_ENGLISH_NAME,
            role: data.system_setting.CHATBOT_ROLE,
            gender: data.system_setting.CHATBOT_GENDER,
            conversationStyle: data.system_setting.CHATBOT_CONVERSATION_STYLE,
            conversationType: data.system_setting.CHATBOT_CONVERSATION_TYPE,
        });
        
        companySettings.set({
            thaiName: data.system_setting.COMPANY_THAI_NAME,
            englishName: data.system_setting.COMPANY_ENGLISH_NAME,
            business: data.system_setting.COMPANY_BUSINESS,
            businessType: data.system_setting.COMPANY_BUSINESS_TYPE,
        });
        
        connectionSettings.set({
            lineChannelSecret: data.system_setting.LINE_CHANNEL_SECRET,
            lineAccessToken: data.system_setting.LINE_ACCESS_TOKEN,
            lineWebhook: data.system_setting.LINE_WEBHOOK,
            lineOAQRCode: data.system_setting.LINE_OA_QR_CODE,
            lineOAQRLink: data.system_setting.LINE_OA_QR_LINK,
            lineGroupQRCode: data.system_setting.LINE_GROUP_QR_CODE,
            lineGroupQRLink: data.system_setting.LINE_GROUP_QR_LINK
        });
        
        transferSettings.set({
            transfer_ticket_partner: data.system_setting.AUTO_TRANSFER_TICKET_PARTNER_CONDITION,
            transfer_ticket_department: data.system_setting.AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION,
            transfer_ticket_tag: data.system_setting.AUTO_TRANSFER_TICKET_USER_TAG_CONDITION
        });
    }
    
    // Function to handle tab changes with refresh and persistence
    function handleTabChange(newTab) {
        if (activeTab !== newTab && availableTabs.includes(newTab)) {
            lastActiveTab = activeTab;
            activeTab = newTab;
            
            // Save the new active tab
            saveActiveTab(newTab);
            
            // Refresh the stores when switching tabs
            refreshAllStores();
            
            // Let the component know we've switched back to it
            dispatchEvent(new CustomEvent('tab-changed', { 
                detail: { previousTab: lastActiveTab, currentTab: newTab } 
            }));
            
            // console.log('Tab changed from', lastActiveTab, 'to', newTab);
        }
    }
    
    // Listen for settings updates from child components
    function handleSettingsUpdated(event) {
        // Refresh stores when settings are updated
        refreshAllStores();
        
        // Show toast notification
        toastMessage = "Settings updated successfully!";
        toastStatus = true;
        counter = 2;
        timeout();
    }
    
    // Toast timeout method
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }
    
    let storedStatus = getSubscriptionFromStorage();

    // Initialize everything on mount
    onMount(() => {
        // Check subscription status 
        if (storedStatus && storedStatus.checked) {
            // If subscription is not active, redirect to subscription page
            if (!storedStatus.is_active) {
                goto('/subscription');
            }
            else {
                // If subscription is active, continue...
                // Initialize active tab first
                initializeActiveTab();
                
                // Then refresh stores
                refreshAllStores();
                
                // Set up listener for settings updates
                window.addEventListener('settings-updated', handleSettingsUpdated);
                
                return () => {
                    window.removeEventListener('settings-updated', handleSettingsUpdated);
                };
            }
        }
        else {
            // If subscription status is not checked, redirect to login
            goto('/login');
        }
    });

    // Reactively update data when it changes from the server
    $: if (data && data.system_setting) {
        refreshAllStores();
    }
</script>

<svelte:head>
    <title>{t('business')}</title>
</svelte:head>

{#if toastStatus}
    <Toast
        id="business-settings-toast-notification"
        color="green"
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus
        class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}

<div id="business-settings-page-container" class="min-h-screen rounded-lg bg-white">
    {#if storedStatus && storedStatus.checked && storedStatus.is_active}
        <div id="business-settings-main-content" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">

            <!-- <Breadcrumb id="business-settings-breadcrumb" aria-label="Default breadcrumb example" class="mb-3">
                <BreadcrumbItem href="/" home>
                <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                <span class="text-gray-400">{t('settings')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                <span class="text-gray-700">{t('business')}</span>
                </BreadcrumbItem>
            </Breadcrumb> -->

            <div id="business-settings-header" class="mb-6">
                <h2 id="business-settings-title" class="text-2xl font-bold">{t('business_settings_title')}</h2>
                <p id="business-settings-description" class="text-gray-600">{t('business_settings_description')}</p>
            </div>

            <div id="business-settings-tab-container" class="bg-white rounded-lg border-b">
                <!-- Navigation Tabs -->
                <div id="business-settings-tab-navigation" class="flex border-b">
                    <button
                        id="business-settings-system-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'system' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'system'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('system')}
                        aria-selected={activeTab === 'system'}
                        role="tab">
                        {t('tab_system')} <!-- System  -->
                    </button>

                    <button
                        id="business-settings-company-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'company' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'company'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('company')}
                        aria-selected={activeTab === 'company'}
                        role="tab">
                        {t('tab_company')}<!-- Company  -->
                    </button>

                    <button
                        id="business-settings-connection-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'connection' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'connection'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('connection')}
                        aria-selected={activeTab === 'connection'}
                        role="tab">
                        {t('tab_connection')}<!-- Connection  -->
                    </button>

                    <button
                        id="business-settings-bot-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'bot' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'bot'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('bot')}
                        aria-selected={activeTab === 'bot'}
                        role="tab">
                        {t('tab_bot')}<!-- AI Chatbot  -->
                    </button>

                    <button
                        id="business-settings-product-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'product' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'product'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('product')}
                        aria-selected={activeTab === 'product'}
                        role="tab">
                        {t('tab_product')}<!-- AI Chatbot  -->
                    </button>

                    <!-- <button
                        id="business-settings-api-steps-tab"
                        class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'api-steps' ? 'text-gray-500 hover:text-gray-700' : ''}"
                        style={activeTab === 'api-steps'
                            ? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
                            : ''}
                        on:click={() => handleTabChange('api-steps')}
                        aria-selected={activeTab === 'api-steps'}
                        role="tab">
                        API Steps
                    </button> -->
                </div>
            </div>

            <!-- Content -->
            <div id="business-settings-tab-content-container">
                <!-- Website Settings -->
                {#if activeTab === 'system'}
                    <div id="business-settings-system-tab-content">
                        <WebsiteSection {systemSettings} on:settings-updated={handleSettingsUpdated}/>
                    </div>
                {/if}

                <!-- Company Settings -->
                {#if activeTab === 'company'}
                    <div id="business-settings-company-tab-content">
                        <CompanySection {companySettings} on:settings-updated={handleSettingsUpdated} />
                    </div>
                {/if}

                <!-- Connection Settings -->
                {#if activeTab === 'connection'}
                    <div id="business-settings-connection-tab-content">
                        <ConnectionSection {connectionSettings} connectors={data.connectors} access_token={data.access_token} on:settings-updated={handleSettingsUpdated}/>
                    </div>
                {/if}

                <!-- Bot Settings -->
                {#if activeTab === 'bot'}
                    <div id="business-settings-bot-tab-content">
                        <ChatbotSection {botSettings} on:settings-updated={handleSettingsUpdated}/>
                    </div>
                {/if}

                {#if activeTab === 'product'}
                    <div id="business-settings-product-tab-content">
                        <ProductSection
                            productProviders={data.productProviders}
                            productTypes={data.productTypes}
                        />
                    </div>
                {/if}

                <!-- API Steps Settings -->
                {#if activeTab === 'api-steps'}
                    <div id="business-settings-api-steps-tab-content">
                        <PolicyClaimsApiStepsSection />
                    </div>
                {/if}
            </div>
        </div>
    {/if}
</div>